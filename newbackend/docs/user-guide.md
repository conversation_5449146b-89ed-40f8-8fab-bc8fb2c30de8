# LangGraph Agent JavaScript 使用指南

## 📋 **目录**

1. [项目概述](#项目概述)
2. [快速开始](#快速开始)
3. [环境配置](#环境配置)
4. [API使用](#api使用)
5. [开发指南](#开发指南)
6. [测试指南](#测试指南)
7. [部署指南](#部署指南)
8. [故障排除](#故障排除)

---

## 🎯 **项目概述**

LangGraph Agent JavaScript是Python LangGraph后端的完整JavaScript/TypeScript迁移版本，提供：

- **完整功能对等**: 与Python版本功能一致
- **现代技术栈**: TypeScript + Express + LangGraphJS
- **高性能**: 优化的异步处理和状态管理
- **类型安全**: 完整的TypeScript类型定义
- **测试覆盖**: 192个测试用例，98.96%通过率

### **核心功能**
- 🔍 **智能研究**: 基于LangGraph的多步骤研究工作流
- 🌐 **网络搜索**: 集成Google搜索和引用管理
- 🤖 **AI推理**: Gemini模型驱动的查询生成和答案合成
- 📊 **状态管理**: 复杂的工作流状态跟踪
- 🔄 **反思机制**: 自动评估和迭代改进

---

## 🚀 **快速开始**

### **1. 环境要求**
```bash
Node.js >= 18.0.0
npm >= 8.0.0
```

### **2. 安装依赖**
```bash
cd newbackend
npm install
```

### **3. 环境配置**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

### **4. 启动开发服务器**
```bash
npm run dev
```

### **5. 运行测试**
```bash
npm test
```

### **6. 构建生产版本**
```bash
npm run build
npm start
```

---

## ⚙️ **环境配置**

### **必需环境变量**
```env
# Gemini API配置
GEMINI_API_KEY=your_gemini_api_key_here

# 模型配置
QUERY_GENERATOR_MODEL=gemini-pro
ANSWER_MODEL=gemini-pro

# 服务器配置
PORT=3000
NODE_ENV=development

# 研究配置
NUMBER_OF_INITIAL_QUERIES=3
MAX_RESEARCH_LOOPS=3
```

### **可选环境变量**
```env
# CORS配置
CORS_ORIGIN=http://localhost:3000

# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 日志配置
LOG_LEVEL=info
```

### **获取Gemini API密钥**
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 创建新的API密钥
3. 将密钥添加到`.env`文件中

---

## 🔌 **API使用**

### **健康检查**
```bash
GET /health
```

**响应:**
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0"
}
```

### **配置信息**
```bash
GET /api/config
```

**响应:**
```json
{
  "queryGeneratorModel": "gemini-pro",
  "answerModel": "gemini-pro",
  "numberOfInitialQueries": 3,
  "maxResearchLoops": 3
}
```

### **前端应用**
```bash
GET /app/*
```
提供静态前端文件服务，支持SPA路由。

---

## 🛠️ **开发指南**

### **项目结构**
```
newbackend/
├── src/
│   ├── agent/
│   │   ├── state.ts      # 状态定义
│   │   ├── schemas.ts    # 数据验证
│   │   ├── config.ts     # 配置管理
│   │   ├── utils.ts      # 工具函数
│   │   ├── prompts.ts    # 提示词模板
│   │   ├── graph.ts      # LangGraph状态图
│   │   └── app.ts        # Express应用
│   └── index.ts          # 应用入口
├── tests/
│   ├── unit/             # 单元测试
│   ├── fixtures/         # 测试数据
│   └── setup.ts          # 测试配置
└── docs/                 # 文档
```

### **核心模块说明**

#### **状态管理 (state.ts)**
- `OverallState`: 主工作流状态
- `ReflectionState`: 反思状态
- `QueryGenerationState`: 查询生成状态
- `WebSearchState`: 网络搜索状态

#### **数据验证 (schemas.ts)**
- 使用Zod进行运行时类型验证
- 支持搜索查询、反思结果、引用等数据结构

#### **配置管理 (config.ts)**
- 环境变量读取和验证
- 默认配置和覆盖机制
- LangGraph配置适配

#### **工具函数 (utils.ts)**
- 消息处理和研究主题提取
- URL解析和引用生成
- 文本清理和验证

#### **LangGraph集成 (graph.ts)**
- 状态图定义和节点实现
- 条件边逻辑
- Gemini API集成

### **添加新功能**

1. **添加新的状态字段**:
```typescript
// 在state.ts中扩展状态定义
export const NewState = Annotation.Root({
  newField: Annotation<string>({
    reducer: (x, y) => y ?? x ?? "",
    default: () => ""
  })
});
```

2. **添加新的验证模式**:
```typescript
// 在schemas.ts中添加Zod模式
export const NewSchema = z.object({
  field: z.string(),
  // ...
});
```

3. **添加新的节点函数**:
```typescript
// 在graph.ts中添加节点
async function newNode(
  state: typeof OverallState.State,
  config?: RunnableConfig
): Promise<Partial<typeof OverallState.State>> {
  // 节点逻辑
  return { /* 更新的状态 */ };
}
```

---

## 🧪 **测试指南**

### **运行测试**
```bash
# 运行所有测试
npm test

# 运行特定测试文件
npm test -- graph.test.ts

# 运行测试并生成覆盖率报告
npm test -- --coverage

# 监视模式
npm test -- --watch
```

### **测试结构**
- **单元测试**: `tests/unit/` - 测试单个模块
- **集成测试**: `tests/integration/` - 测试模块间交互
- **测试数据**: `tests/fixtures/` - 共享测试数据

### **编写测试**
```typescript
import { describe, it, expect } from '@jest/globals';
import { yourFunction } from '../src/your-module.ts';

describe('Your Module', () => {
  it('should do something', () => {
    const result = yourFunction('input');
    expect(result).toBe('expected');
  });
});
```

### **Mock外部依赖**
```typescript
jest.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: jest.fn().mockImplementation(() => ({
    // Mock实现
  }))
}));
```

---

## 📦 **部署指南**

### **生产构建**
```bash
npm run build
```

### **Docker部署**
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["npm", "start"]
```

### **环境变量配置**
确保生产环境中设置所有必需的环境变量：
```bash
export GEMINI_API_KEY=your_production_key
export NODE_ENV=production
export PORT=3000
```

### **健康检查**
```bash
curl http://localhost:3000/health
```

---

## 🔧 **故障排除**

### **常见问题**

#### **1. API密钥错误**
```
Error: Invalid API key
```
**解决方案**: 检查`.env`文件中的`GEMINI_API_KEY`是否正确。

#### **2. 端口占用**
```
Error: Port 3000 is already in use
```
**解决方案**: 更改`PORT`环境变量或停止占用端口的进程。

#### **3. 依赖安装失败**
```
npm ERR! peer dep missing
```
**解决方案**: 删除`node_modules`和`package-lock.json`，重新运行`npm install`。

#### **4. TypeScript编译错误**
```
TS2307: Cannot find module
```
**解决方案**: 检查导入路径是否正确，确保使用`.js`扩展名。

### **调试技巧**

1. **启用详细日志**:
```bash
export LOG_LEVEL=debug
```

2. **检查环境变量**:
```bash
npm run config:check
```

3. **运行健康检查**:
```bash
curl http://localhost:3000/health
```

### **性能优化**

1. **启用生产模式**:
```bash
export NODE_ENV=production
```

2. **调整并发设置**:
```bash
export UV_THREADPOOL_SIZE=16
```

3. **监控内存使用**:
```bash
node --max-old-space-size=4096 dist/index.js
```

---

## 📞 **支持和贡献**

### **获取帮助**
- 查看项目文档: `docs/`
- 检查测试用例: `tests/`
- 查看项目进度: `docs/project-progress.md`

### **贡献指南**
1. Fork项目
2. 创建功能分支
3. 编写测试
4. 提交Pull Request

### **报告问题**
请提供以下信息：
- Node.js版本
- 错误信息
- 重现步骤
- 环境配置
