# LangGraph Agent API 参考文档

## 📋 **目录**

1. [API概述](#api概述)
2. [认证](#认证)
3. [端点列表](#端点列表)
4. [数据模型](#数据模型)
5. [错误处理](#错误处理)
6. [示例代码](#示例代码)

---

## 🎯 **API概述**

LangGraph Agent提供RESTful API，支持智能研究工作流的完整功能。

### **基础信息**
- **基础URL**: `http://localhost:3000`
- **API版本**: `v1`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

### **功能特性**
- 🔍 智能查询生成
- 🌐 网络搜索集成
- 🤖 AI驱动的答案合成
- 📊 实时状态跟踪
- 🔄 自动反思和优化

---

## 🔐 **认证**

当前版本使用API密钥认证（通过环境变量配置）。

### **配置认证**
```env
GEMINI_API_KEY=your_api_key_here
```

### **未来版本**
计划支持：
- JWT Token认证
- OAuth 2.0
- API密钥管理

---

## 📡 **端点列表**

### **1. 健康检查**

#### `GET /health`
检查服务状态和可用性。

**请求示例:**
```bash
curl -X GET http://localhost:3000/health
```

**响应示例:**
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "version": "1.0.0",
  "uptime": 3600,
  "environment": "development"
}
```

**响应字段:**
- `status`: 服务状态 (`ok` | `error`)
- `timestamp`: 响应时间戳
- `version`: 应用版本
- `uptime`: 运行时间（秒）
- `environment`: 运行环境

---

### **2. 配置信息**

#### `GET /api/config`
获取当前应用配置信息。

**请求示例:**
```bash
curl -X GET http://localhost:3000/api/config
```

**响应示例:**
```json
{
  "queryGeneratorModel": "gemini-pro",
  "answerModel": "gemini-pro",
  "numberOfInitialQueries": 3,
  "maxResearchLoops": 3,
  "environment": "development",
  "features": {
    "webSearch": true,
    "reflection": true,
    "citations": true
  }
}
```

**响应字段:**
- `queryGeneratorModel`: 查询生成模型
- `answerModel`: 答案生成模型
- `numberOfInitialQueries`: 初始查询数量
- `maxResearchLoops`: 最大研究循环次数
- `environment`: 运行环境
- `features`: 功能开关

---

### **3. 前端应用**

#### `GET /app/*`
提供前端静态文件服务，支持SPA路由。

**功能特性:**
- 静态资源服务
- SPA路由支持
- 缓存控制
- 压缩传输

**请求示例:**
```bash
curl -X GET http://localhost:3000/app/
curl -X GET http://localhost:3000/app/research
```

**响应:**
- 成功: 返回HTML/CSS/JS文件
- 404: 返回index.html（SPA路由）

---

### **4. 研究工作流 (计划中)**

#### `POST /api/research`
启动新的研究工作流。

**请求体:**
```json
{
  "query": "研究主题或问题",
  "config": {
    "maxLoops": 3,
    "initialQueries": 3,
    "model": "gemini-pro"
  }
}
```

**响应示例:**
```json
{
  "sessionId": "uuid-session-id",
  "status": "started",
  "query": "研究主题或问题",
  "estimatedTime": 30
}
```

#### `GET /api/research/{sessionId}`
获取研究会话状态。

**响应示例:**
```json
{
  "sessionId": "uuid-session-id",
  "status": "running",
  "progress": {
    "currentStep": "web_research",
    "completedSteps": ["generate_query"],
    "totalSteps": 4
  },
  "results": {
    "queries": ["查询1", "查询2"],
    "sources": [...],
    "answer": "部分答案..."
  }
}
```

#### `GET /api/research/{sessionId}/stream`
实时流式获取研究进度。

**响应格式:** Server-Sent Events (SSE)

```
data: {"type": "progress", "step": "generate_query", "progress": 25}
data: {"type": "result", "queries": ["查询1", "查询2"]}
data: {"type": "complete", "answer": "最终答案"}
```

---

## 📊 **数据模型**

### **研究查询 (SearchQuery)**
```typescript
interface SearchQuery {
  query: string;           // 查询文本
  rationale: string;       // 查询理由
  priority: number;        // 优先级 (1-10)
  category?: string;       // 查询类别
}
```

### **研究源 (Source)**
```typescript
interface Source {
  label: string;           // 源标签 (S1, S2, ...)
  shortUrl: string;        // 短URL
  value: string;           // 完整URL
  title?: string;          // 页面标题
  snippet?: string;        // 内容摘要
  relevance?: number;      // 相关性评分
}
```

### **引用 (Citation)**
```typescript
interface Citation {
  startIndex: number;      // 开始位置
  endIndex: number;        // 结束位置
  segments: Source[];      // 引用源
  confidence?: number;     // 置信度
}
```

### **反思结果 (Reflection)**
```typescript
interface Reflection {
  is_sufficient: boolean;     // 信息是否充分
  knowledge_gap: string;      // 知识缺口描述
  follow_up_queries: string[]; // 后续查询建议
  confidence: number;         // 置信度
  reasoning: string;          // 推理过程
}
```

### **研究状态 (ResearchState)**
```typescript
interface ResearchState {
  sessionId: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  query: string;
  messages: Message[];
  searchQueries: SearchQuery[];
  webResearchResults: string[];
  sourcesGathered: Source[];
  citations: Citation[];
  reflection?: Reflection;
  finalAnswer?: string;
  createdAt: string;
  updatedAt: string;
  metadata: {
    researchLoopCount: number;
    maxResearchLoops: number;
    initialSearchQueryCount: number;
    model: string;
  };
}
```

---

## ❌ **错误处理**

### **错误响应格式**
```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细信息",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "requestId": "uuid-request-id"
  }
}
```

### **HTTP状态码**
- `200`: 成功
- `400`: 请求错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `429`: 请求过于频繁
- `500`: 服务器内部错误
- `503`: 服务不可用

### **常见错误码**
- `INVALID_API_KEY`: API密钥无效
- `RATE_LIMIT_EXCEEDED`: 超出速率限制
- `INVALID_REQUEST`: 请求格式错误
- `RESOURCE_NOT_FOUND`: 资源不存在
- `INTERNAL_ERROR`: 内部服务错误
- `SERVICE_UNAVAILABLE`: 服务不可用

### **错误示例**
```json
{
  "error": {
    "code": "INVALID_API_KEY",
    "message": "提供的API密钥无效或已过期",
    "details": "请检查GEMINI_API_KEY环境变量",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "requestId": "req-123456"
  }
}
```

---

## 💻 **示例代码**

### **JavaScript/Node.js**
```javascript
// 健康检查
async function checkHealth() {
  const response = await fetch('http://localhost:3000/health');
  const data = await response.json();
  console.log('服务状态:', data.status);
}

// 获取配置
async function getConfig() {
  const response = await fetch('http://localhost:3000/api/config');
  const config = await response.json();
  console.log('配置信息:', config);
}

// 启动研究 (计划中)
async function startResearch(query) {
  const response = await fetch('http://localhost:3000/api/research', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query: query,
      config: {
        maxLoops: 3,
        initialQueries: 3
      }
    })
  });
  
  const result = await response.json();
  return result.sessionId;
}
```

### **Python**
```python
import requests
import json

# 健康检查
def check_health():
    response = requests.get('http://localhost:3000/health')
    return response.json()

# 获取配置
def get_config():
    response = requests.get('http://localhost:3000/api/config')
    return response.json()

# 启动研究 (计划中)
def start_research(query):
    payload = {
        'query': query,
        'config': {
            'maxLoops': 3,
            'initialQueries': 3
        }
    }
    
    response = requests.post(
        'http://localhost:3000/api/research',
        json=payload
    )
    
    return response.json()['sessionId']
```

### **cURL**
```bash
# 健康检查
curl -X GET http://localhost:3000/health

# 获取配置
curl -X GET http://localhost:3000/api/config

# 启动研究 (计划中)
curl -X POST http://localhost:3000/api/research \
  -H "Content-Type: application/json" \
  -d '{
    "query": "人工智能的发展趋势",
    "config": {
      "maxLoops": 3,
      "initialQueries": 3
    }
  }'
```

---

## 📝 **注意事项**

### **速率限制**
- 默认: 每15分钟100个请求
- 可通过环境变量配置
- 超出限制返回429状态码

### **请求大小限制**
- 请求体最大: 10MB
- URL长度最大: 2048字符

### **超时设置**
- 请求超时: 30秒
- 研究工作流超时: 5分钟

### **版本兼容性**
- API版本: v1
- 向后兼容保证
- 废弃功能提前通知

---

## 🔄 **更新日志**

### **v1.0.0 (当前)**
- ✅ 基础健康检查端点
- ✅ 配置信息端点
- ✅ 前端静态文件服务
- ✅ 错误处理机制

### **v1.1.0 (计划中)**
- 🔄 研究工作流API
- 🔄 实时状态流
- 🔄 会话管理
- 🔄 结果导出

### **v1.2.0 (计划中)**
- 🔄 用户认证
- 🔄 API密钥管理
- 🔄 使用统计
- 🔄 批量处理
