# LangGraph Agent 部署指南

## 📋 **目录**

1. [部署概述](#部署概述)
2. [环境准备](#环境准备)
3. [本地部署](#本地部署)
4. [Docker部署](#docker部署)
5. [云平台部署](#云平台部署)
6. [生产环境配置](#生产环境配置)
7. [监控和维护](#监控和维护)
8. [故障排除](#故障排除)

---

## 🎯 **部署概述**

LangGraph Agent支持多种部署方式，适用于开发、测试和生产环境。

### **部署选项**
- 🖥️ **本地部署**: 开发和测试
- 🐳 **Docker部署**: 容器化部署
- ☁️ **云平台部署**: 生产环境
- 🔄 **CI/CD部署**: 自动化部署

### **系统要求**
- **Node.js**: >= 18.0.0
- **内存**: >= 2GB
- **存储**: >= 10GB
- **网络**: 稳定的互联网连接

---

## 🛠️ **环境准备**

### **1. Node.js安装**
```bash
# 使用nvm安装Node.js
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# 验证安装
node --version  # 应该显示 v18.x.x
npm --version   # 应该显示 8.x.x
```

### **2. 获取源代码**
```bash
# 克隆仓库
git clone <repository-url>
cd gemini-fullstack-langgraph-quickstart/newbackend

# 安装依赖
npm install
```

### **3. 环境变量配置**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

**必需环境变量:**
```env
GEMINI_API_KEY=your_gemini_api_key_here
NODE_ENV=production
PORT=3000
```

---

## 🏠 **本地部署**

### **开发模式**
```bash
# 启动开发服务器
npm run dev

# 服务将在 http://localhost:3000 启动
```

### **生产模式**
```bash
# 构建应用
npm run build

# 启动生产服务器
npm start
```

### **验证部署**
```bash
# 健康检查
curl http://localhost:3000/health

# 配置检查
curl http://localhost:3000/api/config
```

---

## 🐳 **Docker部署**

### **1. 创建Dockerfile**
```dockerfile
# 使用官方Node.js镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY dist ./dist
COPY public ./public

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
USER nextjs

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV NODE_ENV=production

# 启动应用
CMD ["npm", "start"]
```

### **2. 创建.dockerignore**
```
node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.env.local
.env.development
.env.test
coverage
.nyc_output
tests
docs
```

### **3. 构建和运行**
```bash
# 构建Docker镜像
docker build -t langgraph-agent .

# 运行容器
docker run -d \
  --name langgraph-agent \
  -p 3000:3000 \
  -e GEMINI_API_KEY=your_api_key \
  -e NODE_ENV=production \
  langgraph-agent

# 查看日志
docker logs langgraph-agent

# 停止容器
docker stop langgraph-agent
```

### **4. Docker Compose**
```yaml
# docker-compose.yml
version: '3.8'

services:
  langgraph-agent:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - PORT=3000
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - langgraph-agent
    restart: unless-stopped
```

**启动服务:**
```bash
docker-compose up -d
```

---

## ☁️ **云平台部署**

### **1. Vercel部署**
```bash
# 安装Vercel CLI
npm i -g vercel

# 登录Vercel
vercel login

# 部署
vercel --prod
```

**vercel.json配置:**
```json
{
  "version": 2,
  "builds": [
    {
      "src": "dist/index.js",
      "use": "@vercel/node"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "dist/index.js"
    }
  ],
  "env": {
    "NODE_ENV": "production"
  }
}
```

### **2. Heroku部署**
```bash
# 安装Heroku CLI
# 登录Heroku
heroku login

# 创建应用
heroku create langgraph-agent

# 设置环境变量
heroku config:set GEMINI_API_KEY=your_api_key
heroku config:set NODE_ENV=production

# 部署
git push heroku main
```

**Procfile:**
```
web: npm start
```

### **3. AWS EC2部署**
```bash
# 连接到EC2实例
ssh -i your-key.pem ubuntu@your-ec2-ip

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 克隆代码
git clone <repository-url>
cd newbackend

# 安装依赖
npm install

# 构建应用
npm run build

# 使用PM2管理进程
sudo npm install -g pm2
pm2 start dist/index.js --name langgraph-agent
pm2 startup
pm2 save
```

### **4. Google Cloud Run**
```bash
# 构建并推送镜像
gcloud builds submit --tag gcr.io/PROJECT_ID/langgraph-agent

# 部署到Cloud Run
gcloud run deploy langgraph-agent \
  --image gcr.io/PROJECT_ID/langgraph-agent \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars GEMINI_API_KEY=your_api_key
```

---

## 🔧 **生产环境配置**

### **1. 环境变量**
```env
# 生产环境配置
NODE_ENV=production
PORT=3000

# API配置
GEMINI_API_KEY=your_production_api_key

# 安全配置
CORS_ORIGIN=https://yourdomain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json

# 性能配置
UV_THREADPOOL_SIZE=16
NODE_OPTIONS=--max-old-space-size=4096
```

### **2. Nginx反向代理**
```nginx
# nginx.conf
upstream langgraph_agent {
    server 127.0.0.1:3000;
}

server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    location / {
        proxy_pass http://langgraph_agent;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location /health {
        proxy_pass http://langgraph_agent/health;
        access_log off;
    }
}
```

### **3. SSL证书配置**
```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### **4. 防火墙配置**
```bash
# UFW配置
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

---

## 📊 **监控和维护**

### **1. 健康检查**
```bash
# 创建健康检查脚本
#!/bin/bash
# health-check.sh

HEALTH_URL="http://localhost:3000/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE -eq 200 ]; then
    echo "Service is healthy"
    exit 0
else
    echo "Service is unhealthy (HTTP $RESPONSE)"
    exit 1
fi
```

### **2. 日志管理**
```bash
# 使用PM2查看日志
pm2 logs langgraph-agent

# 日志轮转
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

### **3. 性能监控**
```bash
# 安装监控工具
npm install -g clinic

# 性能分析
clinic doctor -- node dist/index.js
clinic bubbleprof -- node dist/index.js
```

### **4. 自动重启**
```bash
# PM2自动重启配置
pm2 start dist/index.js \
  --name langgraph-agent \
  --max-memory-restart 1G \
  --restart-delay 3000
```

---

## 🚨 **故障排除**

### **常见问题**

#### **1. 端口占用**
```bash
# 查找占用端口的进程
sudo lsof -i :3000
sudo netstat -tulpn | grep :3000

# 终止进程
sudo kill -9 <PID>
```

#### **2. 内存不足**
```bash
# 检查内存使用
free -h
top -p $(pgrep node)

# 增加swap空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

#### **3. 磁盘空间不足**
```bash
# 检查磁盘使用
df -h
du -sh /var/log/*

# 清理日志
sudo journalctl --vacuum-time=7d
sudo logrotate -f /etc/logrotate.conf
```

#### **4. API密钥问题**
```bash
# 验证API密钥
curl -H "Authorization: Bearer $GEMINI_API_KEY" \
  https://generativelanguage.googleapis.com/v1/models

# 检查环境变量
echo $GEMINI_API_KEY
```

### **调试命令**
```bash
# 检查服务状态
systemctl status langgraph-agent

# 查看系统日志
journalctl -u langgraph-agent -f

# 网络连接测试
curl -I http://localhost:3000/health
telnet localhost 3000
```

### **备份和恢复**
```bash
# 备份配置
tar -czf backup-$(date +%Y%m%d).tar.gz \
  .env nginx.conf docker-compose.yml

# 恢复配置
tar -xzf backup-20240101.tar.gz
```

---

## 📝 **部署检查清单**

### **部署前检查**
- [ ] Node.js版本 >= 18.0.0
- [ ] 所有依赖已安装
- [ ] 环境变量已配置
- [ ] API密钥有效
- [ ] 构建成功
- [ ] 测试通过

### **部署后验证**
- [ ] 服务正常启动
- [ ] 健康检查通过
- [ ] API端点可访问
- [ ] 日志正常输出
- [ ] 性能指标正常
- [ ] SSL证书有效

### **生产环境检查**
- [ ] 反向代理配置
- [ ] 防火墙规则
- [ ] 监控告警
- [ ] 备份策略
- [ ] 自动重启
- [ ] 日志轮转

---

## 🔄 **更新和维护**

### **应用更新**
```bash
# 拉取最新代码
git pull origin main

# 安装新依赖
npm install

# 重新构建
npm run build

# 重启服务
pm2 restart langgraph-agent
```

### **依赖更新**
```bash
# 检查过时依赖
npm outdated

# 更新依赖
npm update

# 安全审计
npm audit
npm audit fix
```

### **定期维护**
- 每周检查日志和性能
- 每月更新依赖和安全补丁
- 每季度备份和恢复测试
- 每年SSL证书续期
