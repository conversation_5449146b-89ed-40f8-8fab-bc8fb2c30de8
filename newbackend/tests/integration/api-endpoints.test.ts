/**
 * Integration tests for API endpoints
 * Tests the complete API functionality including Express app and middleware
 */

import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import request from 'supertest';
import { configureApp } from '../../src/agent/app.ts';
import type { Express } from 'express';

describe('API Endpoints Integration Tests', () => {
  let app: Express;
  let server: any;

  beforeAll(async () => {
    // Create the Express app
    app = configureApp();
    
    // Start the server on a test port
    const port = process.env.TEST_PORT || 3001;
    server = app.listen(port);
    
    // Wait for server to be ready
    await new Promise(resolve => setTimeout(resolve, 1000));
  });

  afterAll(async () => {
    if (server) {
      await new Promise(resolve => server.close(resolve));
    }
  });

  describe('Health Check Endpoint', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toMatchObject({
        status: 'ok',
        timestamp: expect.any(String),
        version: expect.any(String)
      });

      // Validate timestamp format
      expect(new Date(response.body.timestamp)).toBeInstanceOf(Date);
    });

    it('should include uptime information', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('uptime');
      expect(typeof response.body.uptime).toBe('number');
      expect(response.body.uptime).toBeGreaterThanOrEqual(0);
    });

    it('should have correct content type', async () => {
      await request(app)
        .get('/health')
        .expect('Content-Type', /json/)
        .expect(200);
    });
  });

  describe('Configuration Endpoint', () => {
    it('should return configuration information', async () => {
      const response = await request(app)
        .get('/api/config')
        .expect(200);

      expect(response.body).toMatchObject({
        queryGeneratorModel: expect.any(String),
        answerModel: expect.any(String),
        numberOfInitialQueries: expect.any(Number),
        maxResearchLoops: expect.any(Number)
      });
    });

    it('should return valid model names', async () => {
      const response = await request(app)
        .get('/api/config')
        .expect(200);

      expect(response.body.queryGeneratorModel).toMatch(/gemini/i);
      expect(response.body.answerModel).toMatch(/gemini/i);
    });

    it('should return reasonable numeric values', async () => {
      const response = await request(app)
        .get('/api/config')
        .expect(200);

      expect(response.body.numberOfInitialQueries).toBeGreaterThan(0);
      expect(response.body.numberOfInitialQueries).toBeLessThanOrEqual(10);
      
      expect(response.body.maxResearchLoops).toBeGreaterThan(0);
      expect(response.body.maxResearchLoops).toBeLessThanOrEqual(10);
    });
  });

  describe('Frontend Routes', () => {
    it('should serve frontend app at /app', async () => {
      const response = await request(app)
        .get('/app')
        .expect(200);

      // Should return HTML content or redirect
      expect(response.headers['content-type']).toMatch(/html|text/);
    });

    it('should handle SPA routing for /app/research', async () => {
      const response = await request(app)
        .get('/app/research')
        .expect(200);

      // Should return HTML content for SPA routing
      expect(response.headers['content-type']).toMatch(/html|text/);
    });

    it('should handle nested routes', async () => {
      const response = await request(app)
        .get('/app/some/nested/route')
        .expect(200);

      // Should return HTML content for SPA routing
      expect(response.headers['content-type']).toMatch(/html|text/);
    });
  });

  describe('Error Handling', () => {
    it('should return 404 for unknown API routes', async () => {
      const response = await request(app)
        .get('/api/nonexistent')
        .expect(404);

      expect(response.body).toMatchObject({
        error: expect.any(String)
      });
    });

    it('should return 404 for unknown non-app routes', async () => {
      await request(app)
        .get('/unknown-route')
        .expect(404);
    });

    it('should handle malformed requests gracefully', async () => {
      await request(app)
        .post('/api/config')
        .send('invalid json')
        .expect(400);
    });
  });

  describe('Security Headers', () => {
    it('should include security headers', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      // Check for common security headers
      expect(response.headers).toHaveProperty('x-content-type-options');
      expect(response.headers).toHaveProperty('x-frame-options');
      expect(response.headers).toHaveProperty('x-xss-protection');
    });

    it('should handle CORS properly', async () => {
      const response = await request(app)
        .options('/api/config')
        .expect(204);

      expect(response.headers).toHaveProperty('access-control-allow-origin');
    });
  });

  describe('Content Compression', () => {
    it('should compress responses when requested', async () => {
      const response = await request(app)
        .get('/api/config')
        .set('Accept-Encoding', 'gzip')
        .expect(200);

      // Response should be compressed if large enough
      if (JSON.stringify(response.body).length > 1024) {
        expect(response.headers['content-encoding']).toBe('gzip');
      }
    });
  });

  describe('Rate Limiting', () => {
    it('should allow normal request rates', async () => {
      // Make several requests in quick succession
      const promises = Array(5).fill(null).map(() =>
        request(app).get('/health').expect(200)
      );

      await Promise.all(promises);
    });

    // Note: Rate limiting tests would need to be more sophisticated
    // to actually trigger rate limits in a test environment
  });

  describe('Request Validation', () => {
    it('should validate request headers', async () => {
      await request(app)
        .get('/api/config')
        .set('User-Agent', 'Test Agent')
        .expect(200);
    });

    it('should handle missing headers gracefully', async () => {
      await request(app)
        .get('/health')
        .expect(200);
    });
  });

  describe('Response Format', () => {
    it('should return consistent JSON format', async () => {
      const healthResponse = await request(app)
        .get('/health')
        .expect(200);

      const configResponse = await request(app)
        .get('/api/config')
        .expect(200);

      // Both should be valid JSON
      expect(typeof healthResponse.body).toBe('object');
      expect(typeof configResponse.body).toBe('object');
    });

    it('should include proper timestamps', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      if (response.body.timestamp) {
        const timestamp = new Date(response.body.timestamp);
        expect(timestamp.getTime()).toBeGreaterThan(Date.now() - 10000); // Within last 10 seconds
      }
    });
  });

  describe('Environment Configuration', () => {
    it('should respect environment variables', async () => {
      const response = await request(app)
        .get('/api/config')
        .expect(200);

      // Should reflect current environment configuration
      expect(response.body).toBeDefined();
      expect(Object.keys(response.body).length).toBeGreaterThan(0);
    });
  });
});
