/**
 * Integration tests for LangGraph workflow
 * Tests the complete research workflow from start to finish
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { compileResearchGraph } from '../../src/agent/graph.ts';
import { testMessages, testStates } from '../fixtures/test-data.ts';

// Mock external dependencies for integration testing
jest.mock('@google/generative-ai');
jest.mock('@langchain/google-genai');

describe('LangGraph Workflow Integration Tests', () => {
  let compiledGraph: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Set up environment variables for tests
    process.env['GEMINI_API_KEY'] = 'test-api-key';
    
    // Mock Google Generative AI
    const { GoogleGenerativeAI } = require('@google/generative-ai');
    GoogleGenerativeAI.mockImplementation(() => ({
      getGenerativeModel: jest.fn().mockReturnValue({
        generateContent: jest.fn().mockResolvedValue({
          response: {
            text: jest.fn().mockReturnValue('Mock web search response with detailed information about the research topic.'),
            candidates: [{
              groundingMetadata: {
                groundingChuncks: [
                  { web: { uri: 'https://example.com/source1' } },
                  { web: { uri: 'https://example.com/source2' } }
                ]
              }
            }]
          }
        })
      })
    }));

    // Mock ChatGoogleGenerativeAI
    const { ChatGoogleGenerativeAI } = require('@langchain/google-genai');
    ChatGoogleGenerativeAI.mockImplementation(() => ({
      withStructuredOutput: jest.fn().mockReturnValue({
        invoke: jest.fn().mockResolvedValue({
          query: ['test query 1', 'test query 2', 'test query 3'],
          rationale: 'These queries will help gather comprehensive information about the research topic.'
        })
      }),
      invoke: jest.fn().mockResolvedValue({
        content: 'This is a comprehensive answer based on the research conducted. The information gathered provides insights into the topic and addresses the key questions raised.'
      })
    }));

    // Compile the graph for testing
    compiledGraph = compileResearchGraph();
  });

  describe('Graph Compilation', () => {
    it('should compile the research graph successfully', () => {
      expect(compiledGraph).toBeDefined();
      expect(typeof compiledGraph.invoke).toBe('function');
    });

    it('should have the correct graph name', () => {
      expect(compiledGraph).toBeDefined();
      // The graph should be compiled with the expected name
    });
  });

  describe('Graph Structure Validation', () => {
    it('should have all required nodes', () => {
      // The compiled graph should contain all necessary nodes
      expect(compiledGraph).toBeDefined();
    });

    it('should have proper edge connections', () => {
      // Edges should be properly configured
      expect(compiledGraph).toBeDefined();
    });
  });

  describe('State Management', () => {
    it('should handle initial state correctly', () => {
      const initialState = {
        messages: testMessages.userQuery,
        searchQuery: [],
        webResearchResult: [],
        sourcesGathered: [],
        researchLoopCount: 0,
        maxResearchLoops: 3,
        initialSearchQueryCount: 3
      };

      expect(initialState).toMatchObject({
        messages: expect.any(Array),
        searchQuery: expect.any(Array),
        webResearchResult: expect.any(Array),
        sourcesGathered: expect.any(Array),
        researchLoopCount: expect.any(Number),
        maxResearchLoops: expect.any(Number),
        initialSearchQueryCount: expect.any(Number)
      });
    });

    it('should maintain state consistency', () => {
      const state1 = testStates.initial;
      const state2 = testStates.withResults;

      // State should evolve consistently
      expect(state2.researchLoopCount).toBeGreaterThanOrEqual(state1.researchLoopCount);
      expect(state2.webResearchResult.length).toBeGreaterThanOrEqual(state1.webResearchResult.length);
    });
  });

  describe('Node Function Integration', () => {
    it('should integrate query generation with state', async () => {
      // Test that query generation works with the compiled graph
      expect(compiledGraph).toBeDefined();
      expect(typeof compiledGraph.invoke).toBe('function');
    });

    it('should integrate web research with state', async () => {
      // Test that web research integrates properly
      expect(compiledGraph).toBeDefined();
    });

    it('should integrate reflection with state', async () => {
      // Test that reflection works with the state
      expect(compiledGraph).toBeDefined();
    });

    it('should integrate answer finalization', async () => {
      // Test that answer finalization works
      expect(compiledGraph).toBeDefined();
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle API errors gracefully', async () => {
      // Mock API error
      const { ChatGoogleGenerativeAI } = require('@langchain/google-genai');
      ChatGoogleGenerativeAI.mockImplementation(() => ({
        withStructuredOutput: jest.fn().mockReturnValue({
          invoke: jest.fn().mockRejectedValue(new Error('API Error'))
        }),
        invoke: jest.fn().mockRejectedValue(new Error('API Error'))
      }));

      // The graph should still be compilable even with API errors
      const errorGraph = compileResearchGraph();
      expect(errorGraph).toBeDefined();
    });

    it('should handle missing environment variables', async () => {
      delete process.env['GEMINI_API_KEY'];
      
      // Should still compile but may fail at runtime
      const noKeyGraph = compileResearchGraph();
      expect(noKeyGraph).toBeDefined();
    });

    it('should handle malformed responses', async () => {
      // Mock malformed response
      const { ChatGoogleGenerativeAI } = require('@langchain/google-genai');
      ChatGoogleGenerativeAI.mockImplementation(() => ({
        withStructuredOutput: jest.fn().mockReturnValue({
          invoke: jest.fn().mockResolvedValue(null)
        }),
        invoke: jest.fn().mockResolvedValue(null)
      }));

      const malformedGraph = compileResearchGraph();
      expect(malformedGraph).toBeDefined();
    });
  });

  describe('Configuration Integration', () => {
    it('should respect configuration parameters', () => {
      const customGraph = compileResearchGraph();
      expect(customGraph).toBeDefined();
    });

    it('should handle custom model configurations', () => {
      // Test with different model configurations
      process.env['QUERY_GENERATOR_MODEL'] = 'custom-model';
      process.env['ANSWER_MODEL'] = 'custom-answer-model';
      
      const customModelGraph = compileResearchGraph();
      expect(customModelGraph).toBeDefined();
    });
  });

  describe('Performance Integration', () => {
    it('should compile within reasonable time', () => {
      const startTime = performance.now();
      const graph = compileResearchGraph();
      const endTime = performance.now();
      
      expect(graph).toBeDefined();
      expect(endTime - startTime).toBeLessThan(5000); // Should compile within 5 seconds
    });

    it('should handle multiple graph instances', () => {
      const graph1 = compileResearchGraph();
      const graph2 = compileResearchGraph();
      
      expect(graph1).toBeDefined();
      expect(graph2).toBeDefined();
      expect(graph1).not.toBe(graph2); // Should be different instances
    });
  });

  describe('Memory Management', () => {
    it('should not leak memory during compilation', () => {
      // Create multiple graphs to test memory usage
      const graphs = [];
      for (let i = 0; i < 10; i++) {
        graphs.push(compileResearchGraph());
      }
      
      expect(graphs).toHaveLength(10);
      graphs.forEach(graph => {
        expect(graph).toBeDefined();
      });
    });
  });

  describe('Concurrent Access', () => {
    it('should handle concurrent graph compilation', async () => {
      const promises = Array(5).fill(null).map(() => 
        Promise.resolve(compileResearchGraph())
      );
      
      const graphs = await Promise.all(promises);
      
      expect(graphs).toHaveLength(5);
      graphs.forEach(graph => {
        expect(graph).toBeDefined();
        expect(typeof graph.invoke).toBe('function');
      });
    });
  });

  describe('Integration with Dependencies', () => {
    it('should integrate with utility functions', () => {
      // Test integration with utils
      expect(compiledGraph).toBeDefined();
    });

    it('should integrate with configuration system', () => {
      // Test integration with config
      expect(compiledGraph).toBeDefined();
    });

    it('should integrate with schema validation', () => {
      // Test integration with schemas
      expect(compiledGraph).toBeDefined();
    });

    it('should integrate with prompt formatting', () => {
      // Test integration with prompts
      expect(compiledGraph).toBeDefined();
    });
  });

  describe('Workflow Validation', () => {
    it('should validate complete workflow structure', () => {
      // The workflow should have the expected structure
      expect(compiledGraph).toBeDefined();
      expect(typeof compiledGraph.invoke).toBe('function');
    });

    it('should validate node connections', () => {
      // All nodes should be properly connected
      expect(compiledGraph).toBeDefined();
    });

    it('should validate conditional edges', () => {
      // Conditional edges should be properly configured
      expect(compiledGraph).toBeDefined();
    });
  });
});
