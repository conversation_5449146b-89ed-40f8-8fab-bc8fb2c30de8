.PHONY: all format lint test tests test_watch integration_tests help dev build start install clean frontend backend fullstack

# Default target executed when no arguments are given to make.
all: help

# Define variables
TEST_FILE ?= tests/
FRONTEND_DIR = ../frontend
BACKEND_PORT = 3000
FRONTEND_PORT = 5173

######################
# DEVELOPMENT
######################

# Install dependencies
install:
	@echo "Installing backend dependencies..."
	npm install
	@echo "Installing frontend dependencies..."
	cd $(FRONTEND_DIR) && npm install

# Development mode - start both backend and frontend
dev: install
	@echo "Starting development environment..."
	@echo "Backend will run on http://localhost:$(BACKEND_PORT)"
	@echo "Frontend will run on http://localhost:$(FRONTEND_PORT)"
	@echo "Press Ctrl+C to stop both services"
	@$(MAKE) -j2 backend frontend

# Start backend in development mode
backend:
	@echo "Starting backend on port $(BACKEND_PORT)..."
	npm run dev

# Start frontend in development mode
frontend:
	@echo "Starting frontend on port $(FRONTEND_PORT)..."
	cd $(FRONTEND_DIR) && npm run dev

# Full stack development (backend + frontend)
fullstack: dev

######################
# BUILDING AND PRODUCTION
######################

# Build both backend and frontend for production
build:
	@echo "Building backend..."
	npm run build
	@echo "Building frontend..."
	cd $(FRONTEND_DIR) && npm run build
	@echo "Copying frontend build to backend public directory..."
	@if not exist "public" mkdir public
	@if exist "$(FRONTEND_DIR)\dist" (xcopy /E /I /Y "$(FRONTEND_DIR)\dist\*" "public\") else (echo "Frontend build not found, run 'make build' first")

# Start production server
start: build
	@echo "Starting production server on port $(BACKEND_PORT)..."
	npm start

# Preview production build
preview: build
	@echo "Starting production preview..."
	npm start

######################
# TESTING
######################

# Run all tests
test:
	npm test

# Run tests in watch mode
test_watch:
	npm test -- --watch

# Run specific test file
test_file:
	npm test -- --testPathPattern=$(TEST_FILE)

# Run integration tests only
integration_tests:
	npm test -- --testPathPattern=integration

# Run unit tests only
unit_tests:
	npm test -- --testPathPattern=unit

# Run tests with coverage
test_coverage:
	npm test -- --coverage

######################
# LINTING AND FORMATTING
######################

# Lint code
lint:
	npm run lint

# Format code
format:
	npm run format

# Type check
typecheck:
	npm run typecheck

######################
# UTILITIES
######################

# Clean build artifacts and dependencies
clean:
	@echo "Cleaning backend..."
	@if exist "node_modules" rmdir /S /Q node_modules
	@if exist "dist" rmdir /S /Q dist
	@if exist "coverage" rmdir /S /Q coverage
	@if exist "public" rmdir /S /Q public
	@echo "Cleaning frontend..."
	@if exist "$(FRONTEND_DIR)\node_modules" rmdir /S /Q "$(FRONTEND_DIR)\node_modules"
	@if exist "$(FRONTEND_DIR)\dist" rmdir /S /Q "$(FRONTEND_DIR)\dist"

# Reinstall dependencies
reinstall: clean install

# Check health of the application
health:
	@echo "Checking application health..."
	@curl -f http://localhost:$(BACKEND_PORT)/health || echo "Backend not running"

# Show application status
status:
	@echo "Application Status:"
	@echo "Backend URL: http://localhost:$(BACKEND_PORT)"
	@echo "Frontend URL: http://localhost:$(FRONTEND_PORT)"
	@echo "API Health: http://localhost:$(BACKEND_PORT)/health"
	@echo "API Config: http://localhost:$(BACKEND_PORT)/api/config"

######################
# DOCKER SUPPORT
######################

# Build Docker image
docker_build:
	docker build -t langgraph-agent-js .

# Run Docker container
docker_run:
	docker run -p $(BACKEND_PORT):$(BACKEND_PORT) -e GEMINI_API_KEY=$(GEMINI_API_KEY) langgraph-agent-js

# Docker compose up
docker_up:
	docker-compose up -d

# Docker compose down
docker_down:
	docker-compose down

######################
# DEPLOYMENT
######################

# Deploy to production (placeholder)
deploy:
	@echo "Deployment script not implemented yet"
	@echo "Please refer to docs/deployment-guide.md for deployment instructions"

######################
# HELP
######################

help:
	@echo '==================== LangGraph Agent JavaScript ===================='
	@echo ''
	@echo 'Development Commands:'
	@echo '  make dev                     - start both backend and frontend in development mode'
	@echo '  make backend                 - start only backend in development mode'
	@echo '  make frontend                - start only frontend in development mode'
	@echo '  make fullstack               - alias for dev (start full stack)'
	@echo ''
	@echo 'Build and Production:'
	@echo '  make build                   - build both backend and frontend for production'
	@echo '  make start                   - start production server'
	@echo '  make preview                 - preview production build'
	@echo ''
	@echo 'Testing:'
	@echo '  make test                    - run all tests'
	@echo '  make test_watch              - run tests in watch mode'
	@echo '  make test_file TEST_FILE=<path> - run specific test file'
	@echo '  make integration_tests       - run integration tests only'
	@echo '  make unit_tests              - run unit tests only'
	@echo '  make test_coverage           - run tests with coverage report'
	@echo ''
	@echo 'Code Quality:'
	@echo '  make lint                    - run linters'
	@echo '  make format                  - run code formatters'
	@echo '  make typecheck               - run TypeScript type checking'
	@echo ''
	@echo 'Utilities:'
	@echo '  make install                 - install dependencies for both backend and frontend'
	@echo '  make clean                   - clean build artifacts and dependencies'
	@echo '  make reinstall               - clean and reinstall dependencies'
	@echo '  make health                  - check application health'
	@echo '  make status                  - show application status and URLs'
	@echo ''
	@echo 'Docker:'
	@echo '  make docker_build            - build Docker image'
	@echo '  make docker_run              - run Docker container'
	@echo '  make docker_up               - start with docker-compose'
	@echo '  make docker_down             - stop docker-compose'
	@echo ''
	@echo 'Environment Variables:'
	@echo '  GEMINI_API_KEY               - required for AI functionality'
	@echo '  BACKEND_PORT                 - backend port (default: 3000)'
	@echo '  FRONTEND_PORT                - frontend port (default: 5173)'
	@echo ''
	@echo 'Quick Start:'
	@echo '  1. Set GEMINI_API_KEY environment variable'
	@echo '  2. Run: make dev'
	@echo '  3. Open: http://localhost:5173'
	@echo '==================== LangGraph Agent JavaScript ===================='
